"use client"

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/auth-context'

interface AuthGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  redirectTo?: string
  allowedRoles?: ('student' | 'organizer')[]
}

export function AuthGuard({
  children,
  requireAuth = true,
  redirectTo = '/auth/login',
  allowedRoles
}: AuthGuardProps) {
  // DEVELOPMENT MODE: AuthGuard is disabled for easier navigation and debugging
  console.log('🔓 Development mode: AuthGuard disabled, allowing access to all pages')

  // Simply render children without any authentication checks
  return <>{children}</>
}
